"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model,\n                attachments: config.attachments || [],\n                isRegeneration: config.isRegeneration || false,\n                webSearchEnabled: config.webSearchEnabled || false\n            };\n            console.log(\"=== DEBUG: AI SERVICE REQUEST DATA ===\");\n            console.log(\"Request data:\", JSON.stringify(requestData, null, 2));\n            console.log(\"Attachments length:\", requestData.attachments.length);\n            if (requestData.attachments.length > 0) {\n                console.log(\"First attachment:\", JSON.stringify(requestData.attachments[0], null, 2));\n            }\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});