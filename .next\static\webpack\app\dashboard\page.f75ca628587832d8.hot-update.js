"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch, trackModelSelection } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true,\n        userId: (user === null || user === void 0 ? void 0 : user.email) || null,\n        trackAnalytics: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Atualizar favoritos sempre que o modal for aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedEndpoint && selectedEndpoint.name === \"OpenRouter\" && user) {\n            // Pequeno delay para garantir que os modelos já foram carregados\n            const timer = setTimeout(()=>{\n                updateFavoritesFromFirestore();\n            }, 100);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isOpen,\n        selectedEndpoint,\n        user\n    ]);\n    // Load models when endpoint changes ou não há cache\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Mesmo com cache, atualizar os favoritos se for OpenRouter\n                if (selectedEndpoint.name === \"OpenRouter\" && user) {\n                    updateFavoritesFromFirestore();\n                }\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Função para atualizar apenas os favoritos sem recarregar todos os modelos\n    const updateFavoritesFromFirestore = async ()=>{\n        if (!selectedEndpoint || !user || selectedEndpoint.name !== \"OpenRouter\") return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            console.log(\"Updating favorites from Firestore:\", Array.from(favoriteIds));\n            // Atualizar o estado dos favoritos\n            setFavoriteModelIds(favoriteIds);\n            // Atualizar os modelos com o novo status de favorito\n            setModels((prevModels)=>prevModels.map((model)=>({\n                        ...model,\n                        isFavorite: favoriteIds.has(model.id)\n                    })));\n            // Atualizar o cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const currentCache = prev.get(cacheKey);\n                if (currentCache) {\n                    const updatedModels = currentCache.models.map((model)=>({\n                            ...model,\n                            isFavorite: favoriteIds.has(model.id)\n                        }));\n                    return new Map(prev).set(cacheKey, {\n                        models: updatedModels,\n                        timestamp: currentCache.timestamp\n                    });\n                }\n                return prev;\n            });\n        } catch (error) {\n            console.error(\"Error updating favorites from Firestore:\", error);\n        }\n    };\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs - sempre buscar do Firestore para garantir dados atualizados\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            console.log(\"Loaded favorite IDs:\", Array.from(favoriteIds));\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n            console.log(\"Models loaded with favorites:\", modelsWithFavorites.filter((m)=>m.isFavorite).length, \"favorites found\");\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite for model:\", model.id, \"Current status:\", model.isFavorite);\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Atualizar o cache imediatamente\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const updatedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: updatedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            console.log(\"Optimistic status:\", optimisticNewStatus, \"Actual status:\", actualNewStatus);\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n                // Corrigir o cache\n                if (selectedEndpoint) {\n                    const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                    setModelsCache((prev)=>{\n                        const currentCache = prev.get(cacheKey);\n                        if (currentCache) {\n                            const correctedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                    ...m,\n                                    isFavorite: actualNewStatus\n                                } : m);\n                            return new Map(prev).set(cacheKey, {\n                                models: correctedModels,\n                                timestamp: currentCache.timestamp\n                            });\n                        }\n                        return prev;\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n            // Reverter o cache\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const revertedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: !optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: revertedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Rastrear seleção de modelo\n        trackModelSelection(model.id);\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            // Rastrear seleção de modelo caro\n            trackModelSelection(pendingExpensiveModel.id);\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-blue-100\",\n                                                children: \"Selecionar Modelo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshModels,\n                                                disabled: loading || !selectedEndpoint,\n                                                className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                title: \"Atualizar modelos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                        onChange: (e)=>{\n                                            const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                            setSelectedEndpoint(endpoint || null);\n                                        },\n                                        className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione um endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: endpoint.id,\n                                                    className: \"bg-blue-900 text-blue-100\",\n                                                    children: endpoint.name\n                                                }, endpoint.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Modelo Customizado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"openai/gpt-4-1\",\n                                                        value: customModelId,\n                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUseCustomModel,\n                                                        disabled: !customModelId.trim(),\n                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                        children: \"Usar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20\",\n                                        children: [\n                                            \"paid\",\n                                            \"free\",\n                                            \"favorites\"\n                                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category\n                                                        })),\n                                                className: \"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            value: searchTerm,\n                                                            onChange: setSearchTerm,\n                                                            suggestions: [],\n                                                            isSearching: isSearching,\n                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                            showSuggestions: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: filters.sortBy,\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    sortBy: e.target.value\n                                                                })),\n                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"newest\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Mais recentes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_low\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Menor pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"context_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior contexto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedCategory(null),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                        children: \"Todos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                            title: category.description,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: [\n                                                            filteredModels.length,\n                                                            \" resultado\",\n                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                            ' para \"',\n                                                            searchTerm,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearSearch,\n                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                        children: \"Limpar busca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Tente:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Verificar a ortografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Explorar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Limpar filtros ativos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model),\n                                                onToggleFavorite: ()=>{\n                                                    console.log(\"ModelCard: Toggling favorite for\", model.id, \"Current isFavorite:\", model.isFavorite);\n                                                    handleToggleFavorite(model);\n                                                },\n                                                isToggling: favoriteToggling.has(model.id),\n                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                searchResult: searchResult\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLoadMoreModels,\n                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carregar mais modelos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-400/70\",\n                                                children: [\n                                                    \"Mostrando \",\n                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                    \" de \",\n                                                    filteredModels.length,\n                                                    \" modelos\",\n                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-blue-300\",\n                                                        children: [\n                                                            \"(\",\n                                                            models.length,\n                                                            \" total)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                            searchTerm\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⚡ \",\n                                                            searchResults.length,\n                                                            \" resultados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700/30 space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-slate-100 mb-2\",\n                                            children: \"Modelos DeepSeek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Escolha entre nossos modelos especializados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-400/70 text-sm mt-1\",\n                                                children: \"Tente ajustar os filtros de busca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model)\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 936,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 951,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 font-medium\",\n                                children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400/70 text-sm mt-1\",\n                                children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 963,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"AnO6fQiC71JFFjDSJoOmTEVpuus=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    // Log para debug\n    console.log(\"ModelCard \".concat(model.id, \": isFavorite=\").concat(model.isFavorite, \", isToggling=\").concat(isToggling));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 1011,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1010,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1009,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1008,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 1039,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1064,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1076,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1018,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1093,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1092,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1017,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1001,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>{\n    let { model, isSelected, onSelect } = param;\n    const getModelIcon = (modelId)=>{\n        if (modelId === \"deepseek-chat\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1136,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1135,\n                columnNumber: 9\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1142,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1141,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                children: getModelIcon(model.id)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 text-lg\",\n                                        children: model.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1166,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-300/70 mt-1\",\n                                        children: model.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1165,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Contexto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1187,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onSelect();\n                        },\n                        className: \"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30\" : \"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30\"),\n                        children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1159,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1149,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});