"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/import-chat/page",{

/***/ "(app-pages-browser)/./src/app/import-chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/import-chat/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImportChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ImportChatPage() {\n    _s();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isImporting, setIsImporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [importStatus, setImportStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [importResults, setImportResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Função para buscar username do usuário\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const fetchedUsername = await getUsernameFromFirestore();\n                setUsername(fetchedUsername);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Redirecionar se não estiver autenticado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        authLoading,\n        router\n    ]);\n    const generateChatId = ()=>{\n        return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);\n    };\n    const handleFileSelection = (event)=>{\n        const files = event.target.files;\n        if (files && files.length > 0) {\n            setSelectedFiles(files);\n            setImportResults([]);\n            setImportStatus(\"\\uD83D\\uDCC1 \".concat(files.length, ' arquivo(s) selecionado(s). Clique em \"Importar\" para continuar.'));\n        }\n    };\n    const importSingleChat = async (file)=>{\n        try {\n            if (file.type !== \"application/json\") {\n                return {\n                    filename: file.name,\n                    chatName: \"\",\n                    chatId: \"\",\n                    status: \"error\",\n                    message: \"Arquivo deve ser JSON\"\n                };\n            }\n            const fileContent = await file.text();\n            const importedChat = JSON.parse(fileContent);\n            // Gerar novo ID para evitar conflitos\n            const newChatId = generateChatId();\n            const now = new Date().toISOString();\n            // Preparar dados para o Firestore (formato atual do sistema)\n            const firestoreData = {\n                context: importedChat.context || \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: importedChat.frequency_penalty || 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: importedChat.lastUsedModel || \"\",\n                latexInstructions: importedChat.latexInstructions || false,\n                maxTokens: importedChat.maxTokens || 2048,\n                name: importedChat.name || \"Chat Importado\",\n                password: \"\",\n                repetitionPenalty: importedChat.repetition_penalty || 1.0,\n                sessionTime: {\n                    lastSessionStart: null,\n                    lastUpdated: null,\n                    totalTime: 0\n                },\n                systemPrompt: importedChat.system_prompt || \"\",\n                temperature: importedChat.temperature || 1.0,\n                ultimaMensagem: importedChat.messages.length > 0 ? importedChat.messages[importedChat.messages.length - 1].content.substring(0, 100) + \"...\" : \"Chat importado\",\n                ultimaMensagemEm: importedChat.messages.length > 0 ? new Date(importedChat.messages[importedChat.messages.length - 1].timestamp).toISOString() : now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username, \"conversas\", newChatId), firestoreData);\n            // Preparar dados do chat.json para o Storage (formato atual do sistema)\n            const chatJsonData = {\n                id: newChatId,\n                name: importedChat.name || \"Chat Importado\",\n                messages: importedChat.messages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.role,\n                        timestamp: msg.timestamp,\n                        isFavorite: false,\n                        attachments: [],\n                        ...msg.usage && {\n                            usage: msg.usage\n                        },\n                        ...msg.responseTime && {\n                            responseTime: msg.responseTime\n                        }\n                    })),\n                createdAt: now,\n                lastUpdated: now\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(newChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.uploadBytes)(storageRef, chatJsonBlob);\n            return {\n                filename: file.name,\n                chatName: importedChat.name || \"Chat Importado\",\n                chatId: newChatId,\n                status: \"success\",\n                message: \"Importado com sucesso\"\n            };\n        } catch (error) {\n            console.error(\"Erro ao importar chat:\", error);\n            return {\n                filename: file.name,\n                chatName: \"\",\n                chatId: \"\",\n                status: \"error\",\n                message: error instanceof Error ? error.message : \"Erro desconhecido\"\n            };\n        }\n    };\n    const handleImportChats = async ()=>{\n        if (!selectedFiles || selectedFiles.length === 0 || !username) return;\n        setIsImporting(true);\n        setImportResults([]);\n        setImportStatus(\"\\uD83D\\uDD04 Iniciando importa\\xe7\\xe3o...\");\n        const results = [];\n        const totalFiles = selectedFiles.length;\n        for(let i = 0; i < totalFiles; i++){\n            const file = selectedFiles[i];\n            setImportStatus(\"\\uD83D\\uDD04 Importando \".concat(i + 1, \"/\").concat(totalFiles, \": \").concat(file.name));\n            const result = await importSingleChat(file);\n            results.push(result);\n            setImportResults([\n                ...results\n            ]);\n        }\n        const successCount = results.filter((r)=>r.status === \"success\").length;\n        const errorCount = results.filter((r)=>r.status === \"error\").length;\n        setImportStatus(\"✅ Importa\\xe7\\xe3o conclu\\xedda! \".concat(successCount, \" sucesso(s), \").concat(errorCount, \" erro(s)\"));\n        setIsImporting(false);\n        setSelectedFiles(null);\n        // Limpar o input\n        const fileInput = document.getElementById(\"chat-files\");\n        if (fileInput) fileInput.value = \"\";\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-rafthor flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: \"Carregando...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-rafthor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-white mb-4\",\n                                children: \"Importar Chat do Rafthor Anterior\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-lg\",\n                                children: \"Fa\\xe7a upload do arquivo chat.json para importar suas conversas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 text-sm mt-2\",\n                                children: [\n                                    \"Usu\\xe1rio: \",\n                                    username\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-16 w-16 text-white/60\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"chat-files\",\n                                        className: \"cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 inline-block\",\n                                            children: isImporting ? \"Importando...\" : \"Selecionar arquivos chat.json\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"chat-files\",\n                                        type: \"file\",\n                                        accept: \".json\",\n                                        multiple: true,\n                                        onChange: handleFileSelection,\n                                        disabled: isImporting,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm mt-4\",\n                                        children: \"Selecione um ou m\\xfaltiplos arquivos .json exportados do Rafthor anterior\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedFiles && selectedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImportChats,\n                                            disabled: isImporting,\n                                            className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200\",\n                                            children: isImporting ? \"Importando...\" : \"Importar \".concat(selectedFiles.length, \" arquivo(s)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            importStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-white/5 rounded-lg border border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-center\",\n                                    children: importStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this),\n                            importResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-3\",\n                                        children: \"\\uD83D\\uDCCA Resultados da Importa\\xe7\\xe3o:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-h-60 overflow-y-auto space-y-2\",\n                                        children: importResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(result.status === \"success\" ? \"bg-green-500/10 border-green-500/30 text-green-300\" : \"bg-red-500/10 border-red-500/30 text-red-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: result.filename\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.chatName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-80\",\n                                                                    children: [\n                                                                        \"Chat: \",\n                                                                        result.chatName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-80\",\n                                                                    children: result.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: result.status === \"success\" ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-semibold mb-3\",\n                                children: \"\\uD83D\\uDCCB Instru\\xe7\\xf5es:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-white/80 space-y-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Selecione o arquivo chat.json do seu Rafthor anterior\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• O chat ser\\xe1 importado com um novo ID para evitar conflitos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Todas as mensagens e configura\\xe7\\xf5es ser\\xe3o preservadas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Ap\\xf3s a importa\\xe7\\xe3o, voc\\xea pode acessar o chat no dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            className: \"bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30\",\n                            children: \"← Voltar ao Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\import-chat\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(ImportChatPage, \"rg6gclFbDD/9XAINie8m743AiBE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ImportChatPage;\nvar _c;\n$RefreshReg$(_c, \"ImportChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/import-chat/page.tsx\n"));

/***/ })

});