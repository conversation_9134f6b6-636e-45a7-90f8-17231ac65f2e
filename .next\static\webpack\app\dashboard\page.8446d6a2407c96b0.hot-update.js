"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled,\n            userMessageId: userMessage.id\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    // Funções para drag-n-drop\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            setIsDragOver(true);\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev - 1);\n        // Só remove o overlay quando o contador chega a 0\n        // Isso evita flickering quando o drag passa por elementos filhos\n        if (dragCounter <= 1) {\n            setIsDragOver(false);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Permitir drop apenas se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            e.dataTransfer.dropEffect = \"copy\";\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n        setDragCounter(0);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length === 0) return;\n        // Verificar se temos username necessário\n        if (!currentUsername) {\n            console.error(\"Username n\\xe3o dispon\\xedvel para upload de anexos\");\n            return;\n        }\n        // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = files.length === 1 ? \"Arquivo anexado: \".concat(files[0].name) : \"\".concat(files.length, \" arquivos anexados\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID para anexos\");\n            return;\n        }\n        try {\n            // Importar o attachmentService dinamicamente\n            const { default: attachmentService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\"));\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await attachmentService.uploadMultipleAttachments(files, currentUsername, chatIdToUse);\n            // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos\n            // Isso será feito através de um evento customizado\n            const attachmentMetadata = uploadedAttachments.map((att)=>att.metadata);\n            // Disparar evento customizado para o InputBar capturar\n            const event = new CustomEvent(\"dragDropAttachments\", {\n                detail: {\n                    attachments: attachmentMetadata,\n                    chatId: chatIdToUse,\n                    username: currentUsername\n                }\n            });\n            window.dispatchEvent(event);\n            console.log(\"✅ \".concat(files.length, \" arquivo(s) adicionado(s) como anexo via drag-n-drop\"));\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos via drag-n-drop:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-blue-300 mx-auto animate-bounce\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1235,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-blue-100 mb-2\",\n                                children: \"Solte os arquivos aqui\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: \"Os arquivos ser\\xe3o adicionados como anexos \\xe0 conversa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                        lineNumber: 1234,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1233,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1232,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1326,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1223,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"0zM5LIRSkHCcjq7H4N6QPQswfJ8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});