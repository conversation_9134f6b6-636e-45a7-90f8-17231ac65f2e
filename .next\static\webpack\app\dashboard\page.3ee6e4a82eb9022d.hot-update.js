"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch, trackModelSelection } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true,\n        userId: (user === null || user === void 0 ? void 0 : user.email) || null,\n        trackAnalytics: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Atualizar favoritos sempre que o modal for aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedEndpoint && selectedEndpoint.name === \"OpenRouter\" && user) {\n            // Pequeno delay para garantir que os modelos já foram carregados\n            const timer = setTimeout(()=>{\n                updateFavoritesFromFirestore();\n            }, 100);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isOpen,\n        selectedEndpoint,\n        user\n    ]);\n    // Load models when endpoint changes ou não há cache\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Mesmo com cache, atualizar os favoritos se for OpenRouter\n                if (selectedEndpoint.name === \"OpenRouter\" && user) {\n                    updateFavoritesFromFirestore();\n                }\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Função para atualizar apenas os favoritos sem recarregar todos os modelos\n    const updateFavoritesFromFirestore = async ()=>{\n        if (!selectedEndpoint || !user || selectedEndpoint.name !== \"OpenRouter\") return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            console.log(\"Updating favorites from Firestore:\", Array.from(favoriteIds));\n            // Atualizar o estado dos favoritos\n            setFavoriteModelIds(favoriteIds);\n            // Atualizar os modelos com o novo status de favorito\n            setModels((prevModels)=>prevModels.map((model)=>({\n                        ...model,\n                        isFavorite: favoriteIds.has(model.id)\n                    })));\n            // Atualizar o cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const currentCache = prev.get(cacheKey);\n                if (currentCache) {\n                    const updatedModels = currentCache.models.map((model)=>({\n                            ...model,\n                            isFavorite: favoriteIds.has(model.id)\n                        }));\n                    return new Map(prev).set(cacheKey, {\n                        models: updatedModels,\n                        timestamp: currentCache.timestamp\n                    });\n                }\n                return prev;\n            });\n        } catch (error) {\n            console.error(\"Error updating favorites from Firestore:\", error);\n        }\n    };\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs - sempre buscar do Firestore para garantir dados atualizados\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            console.log(\"Loaded favorite IDs:\", Array.from(favoriteIds));\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n            console.log(\"Models loaded with favorites:\", modelsWithFavorites.filter((m)=>m.isFavorite).length, \"favorites found\");\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite for model:\", model.id, \"Current status:\", model.isFavorite);\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Atualizar o cache imediatamente\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const updatedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: updatedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            console.log(\"Optimistic status:\", optimisticNewStatus, \"Actual status:\", actualNewStatus);\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n                // Corrigir o cache\n                if (selectedEndpoint) {\n                    const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                    setModelsCache((prev)=>{\n                        const currentCache = prev.get(cacheKey);\n                        if (currentCache) {\n                            const correctedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                    ...m,\n                                    isFavorite: actualNewStatus\n                                } : m);\n                            return new Map(prev).set(cacheKey, {\n                                models: correctedModels,\n                                timestamp: currentCache.timestamp\n                            });\n                        }\n                        return prev;\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n            // Reverter o cache\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const revertedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: !optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: revertedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Rastrear seleção de modelo\n        trackModelSelection(model.id);\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            // Rastrear seleção de modelo caro\n            trackModelSelection(pendingExpensiveModel.id);\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-blue-100\",\n                                                children: \"Selecionar Modelo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshModels,\n                                                disabled: loading || !selectedEndpoint,\n                                                className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                title: \"Atualizar modelos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                        onChange: (e)=>{\n                                            const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                            setSelectedEndpoint(endpoint || null);\n                                        },\n                                        className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione um endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: endpoint.id,\n                                                    className: \"bg-blue-900 text-blue-100\",\n                                                    children: endpoint.name\n                                                }, endpoint.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Modelo Customizado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"openai/gpt-4-1\",\n                                                        value: customModelId,\n                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUseCustomModel,\n                                                        disabled: !customModelId.trim(),\n                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                        children: \"Usar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20\",\n                                        children: [\n                                            \"paid\",\n                                            \"free\",\n                                            \"favorites\"\n                                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category\n                                                        })),\n                                                className: \"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            value: searchTerm,\n                                                            onChange: setSearchTerm,\n                                                            suggestions: [],\n                                                            isSearching: isSearching,\n                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                            showSuggestions: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: filters.sortBy,\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    sortBy: e.target.value\n                                                                })),\n                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"newest\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Mais recentes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_low\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Menor pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"context_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior contexto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedCategory(null),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                        children: \"Todos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                            title: category.description,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: [\n                                                            filteredModels.length,\n                                                            \" resultado\",\n                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                            ' para \"',\n                                                            searchTerm,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearSearch,\n                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                        children: \"Limpar busca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Tente:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Verificar a ortografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Explorar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Limpar filtros ativos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model),\n                                                onToggleFavorite: ()=>{\n                                                    console.log(\"ModelCard: Toggling favorite for\", model.id, \"Current isFavorite:\", model.isFavorite);\n                                                    handleToggleFavorite(model);\n                                                },\n                                                isToggling: favoriteToggling.has(model.id),\n                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                searchResult: searchResult\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLoadMoreModels,\n                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carregar mais modelos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-400/70\",\n                                                children: [\n                                                    \"Mostrando \",\n                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                    \" de \",\n                                                    filteredModels.length,\n                                                    \" modelos\",\n                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-blue-300\",\n                                                        children: [\n                                                            \"(\",\n                                                            models.length,\n                                                            \" total)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                            searchTerm\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⚡ \",\n                                                            searchResults.length,\n                                                            \" resultados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700/30 space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-slate-100 mb-2\",\n                                            children: \"Modelos DeepSeek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Escolha entre nossos modelos especializados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-400/70 text-sm mt-1\",\n                                                children: \"Tente ajustar os filtros de busca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model)\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 936,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 951,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 font-medium\",\n                                children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400/70 text-sm mt-1\",\n                                children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 963,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"AnO6fQiC71JFFjDSJoOmTEVpuus=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1007,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1006,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1005,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 1048,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1016,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1082,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1073,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1015,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1109,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1089,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1014,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 998,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>{\n    let { model, isSelected, onSelect } = param;\n    const getModelIcon = (modelId)=>{\n        if (modelId === \"deepseek-chat\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1133,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1132,\n                columnNumber: 9\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1139,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1138,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                children: getModelIcon(model.id)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1159,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 text-lg\",\n                                        children: model.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-300/70 mt-1\",\n                                        children: model.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Contexto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1183,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onSelect();\n                        },\n                        className: \"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30\" : \"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30\"),\n                        children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1146,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});